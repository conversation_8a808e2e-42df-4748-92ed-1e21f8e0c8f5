# -*- coding: utf-8 -*-
"""SQL查询重试机制测试脚本"""

import sys
import os
from datetime import datetime, timedelta

# 导入现有模块
from ai_analysis import analyze_comments_for_level, get_yesterday_date

def test_sql_retry():
    """测试SQL查询重试机制"""
    print("=== SQL查询重试机制测试 ===")
    
    # 获取昨日日期
    yesterday = get_yesterday_date()
    print(f"测试日期: {yesterday}")
    
    # 测试D版大R用户（通常数据较少，容易出现空结果）
    test_cases = [
        ("大R用户", "AND uc.tag_value = '大R'", "D"),
        ("超R用户", "AND uc.tag_value = '超R'", "E")
    ]
    
    for level_name, condition, game_version in test_cases:
        print(f"\n🧪 测试案例: {game_version}版-{level_name}")
        print("=" * 40)
        
        level_key, result = analyze_comments_for_level(
            level_name, 
            condition, 
            yesterday, 
            game_version
        )
        
        print(f"结果: {level_key}")
        if "error" in result:
            print(f"❌ 错误: {result['error']}")
        else:
            print(f"✅ 成功: 获得 {result.get('total_comments', 0)} 条数据")
            print(f"   独立用户: {result.get('unique_users', 0)} 人")

if __name__ == "__main__":
    test_sql_retry() 