# -*- coding: utf-8 -*-
"""AI言论分析脚本 - 使用Gemini API对用户言论进行情绪和话题分析"""

import datetime
import json
import os
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from TA import Ta
from openai import OpenAI

# Gemini API 配置
GEMINI_API_KEY = "AIzaSyB0XJIM5aXH40t_0ogbLqzDpsa-n2-LNhA"
GEMINI_MODEL = "gemini-2.5-pro-preview-06-05"
GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/openai/"

# 重试配置
MAX_RETRIES = 6              # API调用最大重试次数
SQL_RETRY_MAX = 3            # SQL查询最大重试次数
SQL_RETRY_DELAY = 5          # SQL查询重试间隔（秒）
TASK_RETRY_DELAY = 30        # 任务级重试前等待时间（秒）
FAILED_TASK_RETRY_DELAY = 10 # 失败任务重试间隔（秒）
CONCURRENT_WORKERS = 3       # 并发工作线程数

# 初始化Gemini客户端
client = OpenAI(
    api_key=GEMINI_API_KEY,
    base_url=GEMINI_BASE_URL
)

def load_prompt_template():
    """从prompt1.md文件加载提示词模板"""
    try:
        with open('prompt1.md', 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        print("❌ 错误: 未找到prompt1.md文件")
        return None

def get_yesterday_date():
    """获取昨日日期，格式为YYYY-MM-DD"""
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    return yesterday.strftime('%Y-%m-%d')

def get_sql_template(condition, game_version):
    """根据付费层级条件和游戏版本生成对应的SQL查询模板"""
    
    # E版的SQL查询 - 使用与daily_comments.py相同的查询
    if game_version == 'E':
        base_sql = """
        SELECT 
            ev.chat_content AS "言论内容",
            u.role_name AS "角色名"
        FROM v_event_24 ev
        INNER JOIN v_user_24 u ON ev."#user_id" = u."#user_id"
        LEFT JOIN user_result_cluster_24 uc ON ev."#user_id" = uc."#user_id" 
            AND uc.cluster_name = 'tag_20241218_1'
        WHERE ev."$part_event" = 'role_chat'
            AND ev."$part_date" = '{}'
            AND u."#event_date" > 20250529
            AND strpos(ev.chat_content, '[:') = 0
            AND strpos(ev.chat_content, 'color=') = 0
            AND LENGTH(ev.chat_content) > 3
            AND LENGTH(ev.chat_content) < 30
            {}
        GROUP BY ev.chat_content, uc.tag_value, u.role_name
        ORDER BY COUNT(DISTINCT ev."#user_id") DESC
        """
    else:  # D版的SQL查询 - 使用与daily_comments_d.py相同的查询
        base_sql = """
        SELECT 
            ev.content AS "言论内容",
            u.role_name AS "角色名"
        FROM v_event_2 ev
        INNER JOIN v_user_2 u ON ev."#user_id" = u."#user_id"
        LEFT JOIN user_result_cluster_2 uc ON ev."#user_id" = uc."#user_id" 
            AND uc.cluster_name = 'tag_20241211_1'
        WHERE ev."$part_event" = 'Chat'
            AND ev."$part_date" = '{}'
            AND u."#event_date" > 20250529
            AND POSITION('[:' IN ev.content) = 0
            AND POSITION('4297|P|' IN ev.content) = 0
            AND POSITION('color=' IN ev.content) = 0
            AND LENGTH(ev.content) > 3
            AND LENGTH(ev.content) < 30
            {}
        GROUP BY ev.content, uc.tag_value, u.role_name
        ORDER BY COUNT(DISTINCT ev."#user_id") DESC
        """
    
    # 格式化SQL，注意这里使用两层格式化
    return base_sql.format('{}', condition)

def call_gemini_api(messages, max_retries=MAX_RETRIES):
    """调用Gemini API - 增强重试机制"""
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=GEMINI_MODEL,
                messages=messages,
                temperature=0.1,
                max_tokens=6000,
                top_p=0.95
            )
            
            ai_response = response.choices[0].message.content
            
            # 清理响应内容，去掉markdown代码块标记
            if ai_response.startswith('```json'):
                ai_response = ai_response[7:]  # 去掉 ```json
            if ai_response.startswith('```'):
                ai_response = ai_response[3:]   # 去掉 ```
            if ai_response.endswith('```'):
                ai_response = ai_response[:-3]  # 去掉结尾的 ```
            
            return ai_response.strip()
                
        except Exception as e:
            error_str = str(e)
            print(f"API调用异常 (尝试 {attempt + 1}/{max_retries}): {error_str}")
            
            if attempt < max_retries - 1:
                # 根据错误类型确定等待时间
                if "429" in error_str or "rate limit" in error_str.lower():
                    # 速率限制：更长的等待时间
                    wait_time = min(60, 10 * (2 ** attempt))  # 10s, 20s, 40s, 最多60s
                    print(f"⏰ 检测到速率限制，等待 {wait_time} 秒后重试...")
                elif "503" in error_str or "502" in error_str or "server" in error_str.lower():
                    # 服务器错误：中等等待时间
                    wait_time = min(30, 5 * (2 ** attempt))  # 5s, 10s, 20s, 最多30s
                    print(f"⏰ 检测到服务器错误，等待 {wait_time} 秒后重试...")
                elif "connection" in error_str.lower() or "timeout" in error_str.lower():
                    # 连接错误：较短等待时间
                    wait_time = min(15, 3 * (2 ** attempt))  # 3s, 6s, 12s, 最多15s
                    print(f"⏰ 检测到连接错误，等待 {wait_time} 秒后重试...")
                else:
                    # 其他错误：标准指数退避
                    wait_time = min(20, 2 ** attempt)  # 1s, 2s, 4s, 8s, 16s, 最多20s
                    print(f"⏰ 其他错误，等待 {wait_time} 秒后重试...")
                
                time.sleep(wait_time)
            else:
                print(f"❌ API调用最终失败，已达到最大重试次数 ({max_retries})")
    
    return None

def analyze_comments_for_level(level_name, condition, yesterday, game_version):
    """分析单个付费层级的言论数据 - 增加SQL查询重试机制"""
    print(f"\n🔄 开始分析: {game_version}版-{level_name}")
    
    try:
        # 初始化数据库查询客户端
        ta = Ta(game_version)
        
        # 生成SQL语句
        sql_template = get_sql_template(condition, game_version)
        sql = sql_template.format(yesterday)
        
        # SQL查询重试机制
        result = None
        sql_retry_count = 0
        
        while sql_retry_count < SQL_RETRY_MAX:
            print(f"🔍 {game_version}版-{level_name}: 执行SQL查询 (尝试 {sql_retry_count + 1}/{SQL_RETRY_MAX})")
            result = ta.sql_query(sql)
            
            if result is not None and not result.empty:
                print(f"📊 {game_version}版-{level_name}: 获得 {len(result)} 条言论数据")
                break
            else:
                sql_retry_count += 1
                if sql_retry_count < SQL_RETRY_MAX:
                    print(f"⚠️ {game_version}版-{level_name}: 查询结果为空，等待 {SQL_RETRY_DELAY} 秒后重试...")
                    time.sleep(SQL_RETRY_DELAY)
                else:
                    print(f"❌ {game_version}版-{level_name}: SQL查询最终失败，已达到最大重试次数 ({SQL_RETRY_MAX})")
        
        # 如果所有重试都失败，返回错误
        if result is None or result.empty:
            print(f"❌ {game_version}版-{level_name}: 查询结果为空 (已重试 {SQL_RETRY_MAX} 次)")
            return f"{game_version}版-{level_name}", {"error": f"查询结果为空 (已重试 {SQL_RETRY_MAX} 次)"}
        
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        
        # 保存原始查询数据为JSON
        raw_data_file = f"data/raw_data_{game_version}版_{level_name}_{yesterday}.json"
        result_json = result.to_json(orient='records', force_ascii=False, indent=2)
        with open(raw_data_file, 'w', encoding='utf-8') as f:
            f.write(result_json)
        print(f"💾 {game_version}版-{level_name}: 原始数据已保存至 {raw_data_file}")
        
        # 全量分析（不再限制条数）
        sample_size = len(result)
        sample_result = result
        
        # 准备AI分析的输入数据 - 使用CSV格式
        csv_header = "言论内容,角色名"
        csv_lines = [csv_header]
        for _, row in sample_result.iterrows():
            # 转义CSV中的逗号和引号
            content = str(row['言论内容']).replace('"', '""')
            role_name = str(row['角色名']).replace('"', '""')
            # 如果内容包含逗号，需要用引号包围
            if ',' in content:
                content = f'"{content}"'
            if ',' in role_name:
                role_name = f'"{role_name}"'
            csv_lines.append(f"{content},{role_name}")
        
        comments_text = "\n".join(csv_lines)
        
        # 加载提示词模板
        prompt_template = load_prompt_template()
        
        # 构建AI对话消息
        messages = [
            {
                "role": "system",
                "content": f"当前分析的用户层级：{level_name}\n\n{prompt_template}"
            },
            {
                "role": "user", 
                "content": f"请分析以下{level_name}的言论数据（共{len(result)}条，CSV格式，表头为：言论内容,角色名）：\n\n{comments_text}"
            }
        ]
        
        # 调用AI API - 增强重试机制
        print(f"🤖 {game_version}版-{level_name}: 正在调用AI分析...")
        
        # 第一次尝试
        ai_response = call_gemini_api(messages)
        
        # 如果第一次失败，等待后再次尝试
        if not ai_response:
            print(f"⚠️ {game_version}版-{level_name}: 第一次分析失败，等待{TASK_RETRY_DELAY}秒后重试...")
            time.sleep(TASK_RETRY_DELAY)
            ai_response = call_gemini_api(messages, max_retries=3)  # 第二次尝试，减少重试次数
        
        if ai_response:
            print(f"✅ {game_version}版-{level_name}: AI分析完成")
            # 调试：打印AI响应的前200个字符
            print(f"📝 {game_version}版-{level_name}: AI响应预览: {ai_response[:200]}...")
            # 统计独立用户数
            unique_users = len(sample_result['角色名'].unique())
            
            return f"{game_version}版-{level_name}", {
                "ai_response": ai_response,
                "total_comments": len(result),
                "analyzed_comments": sample_size,
                "unique_users": unique_users
            }
        else:
            print(f"❌ {game_version}版-{level_name}: AI分析最终失败（已尝试多次重试）")
            return f"{game_version}版-{level_name}", {"error": "AI分析失败（多次重试后）"}
            
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ {game_version}版-{level_name}: 分析过程出错 - {e}")
        print(f"详细错误信息: {error_details}")
        return f"{game_version}版-{level_name}", {"error": f"分析过程出错: {str(e)}", "traceback": error_details}

def main():
    """主函数 - 多线程AI分析不同版本和付费层级的言论数据"""
    print("=== AI言论分析系统 ===")
    
    # 获取昨日日期
    yesterday = get_yesterday_date()
    print(f"分析日期: {yesterday}")
    print(f"使用模型: {GEMINI_MODEL}")
    
    # 定义不同付费层级的查询条件
    pay_levels_e = [
        ("小R用户", "AND uc.tag_value = '小R'"),
        ("中R用户", "AND uc.tag_value = '中R'"),
        ("大R用户", "AND uc.tag_value = '大R'"),
        ("超R用户", "AND uc.tag_value = '超R'"),
        ("零氪用户", "AND uc.tag_value IS NULL")
    ]
    
    pay_levels_d = [
        ("小R用户", "AND uc.tag_value = '小R'"),
        ("中R用户", "AND uc.tag_value = '中R'"),
        ("大R用户", "AND uc.tag_value = '大R'"),
        ("超R用户", "AND uc.tag_value = '超R'"),
        ("零氪用户", "AND uc.tag_value = '非R'")
    ]
    
    # 定义游戏版本
    game_versions = ['D', 'E']
    
    # 使用线程池并发执行分析
    results = {}
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=CONCURRENT_WORKERS) as executor:  # 使用配置的并发数
        # 提交所有分析任务
        future_to_level = {}
        for game_version in game_versions:
            # 根据游戏版本选择对应的付费层级配置
            pay_levels = pay_levels_e if game_version == 'E' else pay_levels_d
            
            for level_name, condition in pay_levels:
                future = executor.submit(analyze_comments_for_level, level_name, condition, yesterday, game_version)
                future_to_level[future] = f"{game_version}版-{level_name}"
        
        # 收集结果
        for future in as_completed(future_to_level):
            level_key, analysis_result = future.result()
            results[level_key] = analysis_result
    
    # 检查失败的任务并重试
    failed_tasks = []
    for level_key, result_data in results.items():
        if result_data and "error" in result_data:
            failed_tasks.append(level_key)
    
    # 对失败的任务进行串行重试（避免并发压力）
    if failed_tasks:
        print(f"\n🔄 检测到 {len(failed_tasks)} 个失败任务，开始串行重试...")
        for level_key in failed_tasks:
            print(f"🔄 重试任务: {level_key}")
            time.sleep(FAILED_TASK_RETRY_DELAY)  # 使用配置的重试间隔
            
            # 解析level_key获取参数
            parts = level_key.split('版-')
            game_version = parts[0]
            level_name = parts[1]
            
            # 获取对应的查询条件
            pay_levels = pay_levels_e if game_version == 'E' else pay_levels_d
            condition = None
            for name, cond in pay_levels:
                if name == level_name:
                    condition = cond
                    break
            
            if condition:
                retry_key, retry_result = analyze_comments_for_level(level_name, condition, yesterday, game_version)
                results[retry_key] = retry_result
                if retry_result and "error" not in retry_result:
                    print(f"✅ 重试成功: {retry_key}")
                else:
                    print(f"❌ 重试仍失败: {retry_key}")
    
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    # 保存结果
    output_file = f"data/ai_analysis_{yesterday}.json"
    final_results = {}
    
    for level_key in results:
        result_data = results[level_key]
        if result_data and "ai_response" in result_data:
            # 直接保存AI返回的原始响应，不做JSON解析
            final_results[level_key] = {
                "ai_response": result_data["ai_response"],
                "metadata": {
                    "total_comments": result_data.get("total_comments", 0),
                    "analyzed_comments": result_data.get("analyzed_comments", 0),
                    "unique_users": result_data.get("unique_users", 0)
                }
            }
            print(f"✅ {level_key}: 结果已保存")
        else:
            final_results[level_key] = result_data or {"error": "分析失败"}
    
    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(final_results, f, ensure_ascii=False, indent=2)
    
    end_time = time.time()
    print(f"\n{'='*60}")
    print(f"✅ 所有分析完成!")
    print(f"📁 结果已保存至: {output_file}")
    print(f"⏱️ 总耗时: {end_time - start_time:.1f} 秒")
    print(f"{'='*60}")

if __name__ == "__main__":
    main() 