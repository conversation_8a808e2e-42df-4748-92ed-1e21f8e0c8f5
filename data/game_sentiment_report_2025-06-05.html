<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏舆情可视化日报</title>
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* 主色调 */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-accent: #EFF6FF;
            --accent-primary: #3B82F6;
            --accent-secondary: #10B981;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --border-light: #E5E7EB;
            --border-medium: #D1D5DB;
            
            /* 用户层级色彩 */
            --super-r: linear-gradient(135deg, #A855F7, #C084FC);
            --big-r: linear-gradient(135deg, #3B82F6, #60A5FA);
            --mid-r: linear-gradient(135deg, #10B981, #34D399);
            --small-r: linear-gradient(135deg, #F59E0B, #FBBF24);
            --zero-pay: linear-gradient(135deg, #8B5CF6, #A78BFA);
            
            /* 健康度色彩 */
            --health-excellent: #059669;
            --health-good: #0284C7;
            --health-warning: #EA580C;
            --health-critical: #DC2626;
            
            /* 风险等级色彩 */
            --risk-high: #DC2626;
            --risk-medium: #EA580C;
            --risk-low: #059669;
            
            /* 字体大小 */
            --font-title-main: 2.5rem;
            --font-title-sub: 2rem;
            --font-title-card: 1.5rem;
            --font-body: 1rem;
            --font-caption: 0.875rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', 'Microsoft YaHei', 'Segoe UI', system-ui, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .dashboard-container {
            display: grid;
            grid-template-areas:
                "overview overview overview"
                "topics risks health"
                "compare wordcloud players";
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 1.5rem;
            padding: 2rem;
            min-height: 100vh;
        }

        .card {
            background: var(--bg-secondary);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: var(--border-medium);
        }

        .card h3 {
            color: var(--text-primary);
            font-size: var(--font-title-card);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card h3 i {
            color: var(--accent-primary);
        }

        /* 主卡片：舆情总览 */
        .main-overview-card {
            grid-area: overview;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .title-section h1 {
            font-size: var(--font-title-main);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-section h1 i {
            color: var(--accent-primary);
        }

        .date-badge {
            background: var(--accent-primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .health-score-large {
            display: flex;
            align-items: center;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            text-align: center;
            position: relative;
        }

        .score-value {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .score-label {
            font-size: var(--font-caption);
            margin-top: 0.25rem;
        }

        .global-metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric-item:hover {
            background: var(--bg-accent);
            transform: translateY(-1px);
        }

        .metric-item i {
            font-size: 1.5rem;
            color: var(--accent-primary);
            width: 40px;
            text-align: center;
        }

        .metric-content .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            display: block;
        }

        .metric-content .label {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .core-insight {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-primary);
        }

        .core-insight h3 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .core-insight p {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .insight-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .insight-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .insight-tag.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .insight-tag.warning {
            background: #FEF3C7;
            color: #92400E;
        }

        .insight-tag.positive {
            background: #DCFCE7;
            color: #166534;
        }

        /* 热点话题卡片 */
        .hot-topics-card {
            grid-area: topics;
        }

        .topics-ranking {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .topic-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-secondary);
            transition: all 0.3s ease;
        }

        .topic-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .topic-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .topic-header .rank {
            background: var(--accent-secondary);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-caption);
            font-weight: 600;
        }

        .topic-header .title {
            flex: 1;
            margin-left: 1rem;
            font-weight: 600;
        }

        .impact-badge {
            background: var(--bg-accent);
            color: var(--accent-primary);
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
        }

        .topic-details {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        .negative-rate, .affected-levels {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .representative-quote {
            font-style: italic;
            color: var(--text-secondary);
            font-size: var(--font-caption);
            border-left: 2px solid var(--border-light);
            padding-left: 0.75rem;
        }

        /* 风险预警卡片 */
        .risk-radar-card {
            grid-area: risks;
        }

        .risk-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .risk-category {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--risk-medium);
        }

        .risk-category.severity-高 {
            border-left-color: var(--risk-high);
        }

        .risk-category.severity-中 {
            border-left-color: var(--risk-medium);
        }

        .risk-category.severity-低 {
            border-left-color: var(--risk-low);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .category-name {
            font-weight: 600;
            flex: 1;
        }

        .severity-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
            color: white;
            background: var(--risk-medium);
        }

        .severity-badge.severity-高 {
            background: var(--risk-high);
        }

        .severity-badge.severity-中 {
            background: var(--risk-medium);
        }

        .severity-badge.severity-低 {
            background: var(--risk-low);
        }

        .affected-groups {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .level-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .risk-description {
            color: var(--text-secondary);
            font-size: var(--font-caption);
            line-height: 1.6;
        }

        /* 用户群体健康度卡片 */
        .user-health-card {
            grid-area: health;
        }

        .health-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .level-health-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .level-info {
            flex: 1;
        }

        .level-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .user-count {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .health-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 1.2rem;
        }

        .level-status {
            text-align: center;
        }

        .status-text {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            display: block;
        }

        .trend-arrow {
            font-size: 1.5rem;
            margin-top: 0.25rem;
        }

        /* 版本对比卡片 */
        .version-compare-card {
            grid-area: compare;
        }

        .compare-container {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .version-column {
            flex: 1;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }

        .version-column h4 {
            margin-bottom: 1rem;
            color: var(--accent-primary);
            font-size: 1.2rem;
        }

        .version-metrics {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .version-metrics .health-score {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .version-metrics .risk-count,
        .version-metrics .hot-issue {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .vs-divider {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-primary);
        }

        .key-differences {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
        }

        .key-differences h5 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .key-differences ul {
            list-style: none;
        }

        .key-differences li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-light);
            color: var(--text-secondary);
            font-size: var(--font-caption);
        }

        /* 词云卡片 */
        .wordcloud-card {
            grid-area: wordcloud;
        }

        .word-cloud-container {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            min-height: 200px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .word-bubble {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .word-bubble[data-sentiment="negative"] {
            background: #FEE2E2;
            color: #991B1B;
        }

        .word-bubble[data-sentiment="neutral"] {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .word-bubble[data-sentiment="positive"] {
            background: #DCFCE7;
            color: #166534;
        }

        .word-bubble:hover {
            transform: scale(1.1);
        }

        .sentiment-legend {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: var(--font-caption);
        }

        .legend-item:before {
            content: '';
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .legend-item.positive:before {
            background: #166534;
        }

        .legend-item.neutral:before {
            background: var(--accent-primary);
        }

        .legend-item.negative:before {
            background: #991B1B;
        }

        /* 重点关注玩家卡片 */
        .key-players-card {
            grid-area: players;
        }

        .players-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .player-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .player-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--accent-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .player-info {
            flex: 1;
        }

        .player-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .player-level {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .player-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .message-count {
            font-weight: 600;
            color: var(--text-primary);
        }

        .sentiment-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .sentiment-badge.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .sentiment-badge.neutral {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .sentiment-badge.positive {
            background: #DCFCE7;
            color: #166534;
        }

        .attention-reason {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            line-height: 1.5;
            max-width: 200px;
        }

        /* 健康度分数样式 */
        .health-indicator[data-score^="8"],
        .health-indicator[data-score^="9"],
        .score-circle[data-score^="8"],
        .score-circle[data-score^="9"] {
            background: var(--health-excellent);
        }

        .health-indicator[data-score^="6"],
        .health-indicator[data-score^="7"],
        .score-circle[data-score^="6"],
        .score-circle[data-score^="7"] {
            background: var(--health-good);
        }

        .health-indicator[data-score^="4"],
        .health-indicator[data-score^="5"],
        .score-circle[data-score^="4"],
        .score-circle[data-score^="5"] {
            background: var(--health-warning);
        }

        .health-indicator[data-score^="0"],
        .health-indicator[data-score^="1"],
        .health-indicator[data-score^="2"],
        .health-indicator[data-score^="3"],
        .score-circle[data-score^="0"],
        .score-circle[data-score^="1"],
        .score-circle[data-score^="2"],
        .score-circle[data-score^="3"] {
            background: var(--health-critical);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-areas:
                    "overview overview"
                    "topics risks"
                    "health compare"
                    "wordcloud players";
                grid-template-columns: 1fr 1fr;
            }

            .global-metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-areas:
                    "overview"
                    "topics"
                    "risks"
                    "health"
                    "compare"
                    "wordcloud"
                    "players";
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .global-metrics-grid {
                grid-template-columns: 1fr;
            }

            .overview-header {
                flex-direction: column;
                gap: 1rem;
            }

            .compare-container {
                flex-direction: column;
            }

            .vs-divider {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 主卡片：舆情总览 -->
        <div class="main-overview-card card">
            <div class="overview-header">
                <div class="title-section">
                    <h1><i class="fas fa-gamepad"></i> 游戏舆情日报</h1>
                    <div class="date-badge">2024年05月21日</div>
                </div>
                <div class="health-score-large">
                    <div class="score-circle" data-score="53">
                        <span class="score-value">53</span>
                        <span class="score-label">综合健康度</span>
                    </div>
                </div>
            </div>
            
            <div class="global-metrics-grid">
                <div class="metric-item">
                    <i class="fas fa-comments"></i>
                    <div class="metric-content">
                        <span class="value">300,978</span>
                        <span class="label">总消息数</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-users"></i>
                    <div class="metric-content">
                        <span class="value">19,406</span>
                        <span class="label">活跃用户</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="metric-content">
                        <span class="value">20</span>
                        <span class="label">高风险警报</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-chart-line"></i>
                    <div class="metric-content">
                        <span class="value">42.3%</span>
                        <span class="label">负面情绪占比</span>
                    </div>
                </div>
            </div>
            
            <div class="core-insight">
                <h3><i class="fas fa-lightbulb"></i> 今日核心洞察</h3>
                <p>今日舆情的核心风暴眼是“聊天系统无法输入数字”的技术故障，该问题已蔓延至所有用户层级和版本，严重破坏了基础社交体验，引发大规模负面口碑。付费玩家对“洗练”和“抽卡”等随机玩法的回报率极度不满，认为投入与产出严重失衡，已出现停氪甚至退游的危险信号。E版用户对“巨牙行动”的肝度和低回报感到疲惫，而D版用户则更关注资源获取和PVP环境恶化。整体来看，游戏正面临核心体验受损和付费用户流失的双重高风险。</p>
                <div class="insight-tags">
                    <span class="insight-tag negative">沟通体验严重受损</span>
<span class="insight-tag negative">付费体验不佳</span>
<span class="insight-tag warning">核心玩法倦怠</span>
<span class="insight-tag negative">用户流失风险上升</span>
                </div>
            </div>
        </div>

        <!-- 热点话题卡片 -->
        <div class="hot-topics-card card">
            <h3><i class="fas fa-fire"></i> 全服热点话题</h3>
            <div class="topics-ranking">
                <div class="topic-item">
    <div class="topic-header">
        <span class="rank">#1</span>
        <span class="title">聊天系统无法输入数字</span>
        <span class="impact-badge">讨论量 12k+</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 98%</div>
        <div class="affected-levels">影响所有用户层级</div>
    </div>
    <div class="representative-quote">"现在好多数字都打不出来，策划脑子被驴踢了？"</div>
</div>
<div class="topic-item">
    <div class="topic-header">
        <span class="rank">#2</span>
        <span class="title">装备洗练/抽卡出货率低</span>
        <span class="impact-badge">讨论量 4k+</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 85%</div>
        <div class="affected-levels">超R, 大R, 中R</div>
    </div>
    <div class="representative-quote">"红色融了都是a，别人蓝色融了都有sss拉满"</div>
</div>
<div class="topic-item">
    <div class="topic-header">
        <span class="rank">#3</span>
        <span class="title">巨牙活动/体力消耗问题</span>
        <span class="impact-badge">讨论量 6k+</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 68%</div>
        <div class="affected-levels">E版全体用户</div>
    </div>
    <div class="representative-quote">"这个巨牙活动太费体力了，定位一次，还得用体力在打一次"</div>
</div>
            </div>
        </div>

        <!-- 风险预警雷达 -->
        <div class="risk-radar-card card">
            <h3><i class="fas fa-exclamation-triangle"></i> 风险预警雷达</h3>
            <div class="risk-matrix">
                <div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">核心体验受损/缺失</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">全用户</span>
    </div>
    <div class="risk-description">聊天系统屏蔽数字的BUG严重影响了基础沟通、社交和指挥功能，是当前最普遍、最严重的风险点，引发大规模负面口碑。</div>
</div>
<div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">付费体验差/流失风险</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
        <span class="level-tag">中R</span>
    </div>
    <div class="risk-description">付费用户普遍认为“洗练”、“抽卡”、“超导”等核心付费系统回报率极低，投入与产出严重失衡，已出现“停氪”、“摆烂”等危险信号。</div>
</div>
<div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">玩家留存与倦怠风险</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">小R</span>
        <span class="level-tag">零氪</span>
    </div>
    <div class="risk-description">核心活动（如巨牙）投入产出比低，资源获取困难，PVP挫败感强，导致非R和小R玩家群体产生强烈倦怠感和流失意向。</div>
</div>
<div class="risk-category severity-中">
    <div class="category-header">
        <span class="category-name">社区生态恶化风险</span>
        <span class="severity-badge severity-中">中</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">全用户</span>
    </div>
    <div class="risk-description">“炸矿”等恶意PVP行为频发，玩家间矛盾激化，同时核心玩家开始担忧玩家流失导致“死区”，悲观情绪正在蔓延。</div>
</div>
            </div>
        </div>

        <!-- 用户群体健康度 -->
        <div class="user-health-card card">
            <h3><i class="fas fa-users"></i> 用户群体健康度</h3>
            <div class="health-matrix">
                <div class="level-health-item">
    <div class="level-info">
        <div class="level-name">超R用户</div>
        <div class="user-count">567人</div>
    </div>
    <div class="health-indicator" data-score="56">
        <span class="score">56</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">大R用户</div>
        <div class="user-count">1,913人</div>
    </div>
    <div class="health-indicator" data-score="57">
        <span class="score">57</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">中R用户</div>
        <div class="user-count">4,969人</div>
    </div>
    <div class="health-indicator" data-score="47">
        <span class="score">47</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">小R用户</div>
        <div class="user-count">8,062人</div>
    </div>
    <div class="health-indicator" data-score="50">
        <span class="score">50</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">零氪用户</div>
        <div class="user-count">3,895人</div>
    </div>
    <div class="health-indicator" data-score="47">
        <span class="score">47</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
            </div>
        </div>

        <!-- 版本对比分析 -->
        <div class="version-compare-card card">
            <h3><i class="fas fa-balance-scale"></i> 版本对比分析</h3>
            <div class="compare-container">
                <div class="version-column">
                    <h4>E版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">58</div>
                        <div class="risk-count">10个风险点</div>
                        <div class="hot-issue">巨牙活动与奖励</div>
                    </div>
                </div>
                <div class="vs-divider">VS</div>
                <div class="version-column">
                    <h4>D版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">47</div>
                        <div class="risk-count">10个风险点</div>
                        <div class="hot-issue">聊天系统无法使用</div>
                    </div>
                </div>
            </div>
            <div class="key-differences">
                <h5>关键差异</h5>
                <ul>
                    <li>E版用户抱怨集中于“巨牙活动”肝度和“体力”不足，核心痛点在PVE体验。</li>
<li>D版用户抱怨集中于“资源”短缺和“PVP环境”恶化，核心痛点在成长与对抗。</li>
<li>两个版本均受“数字屏蔽”问题严重困扰，但D版讨论量更大，影响更深。</li>
<li>付费体验方面，E版更关注“洗练”机制，D版更关注“抽卡/图纸”概率。</li>
<li>整体健康度E版（58分）略高于D版（47分），但两者均处于“一般”水平，面临严峻挑战。</li>
                </ul>
            </div>
        </div>

        <!-- 舆情词云分析 -->
        <div class="wordcloud-card card">
            <h3><i class="fas fa-tags"></i> 舆情词云分析</h3>
            <div class="word-cloud-container">
                <span class="word-bubble" data-sentiment="negative" data-frequency="3808" style="font-size: 2rem;">数字</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="1066" style="font-size: 2rem;">体力</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="1983" style="font-size: 2rem;">资源</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="2221" style="font-size: 2rem;">打不过</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="814" style="font-size: 2rem;">巨牙</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="540" style="font-size: 1.8rem;">洗练</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="1003" style="font-size: 2rem;">氪金</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="943" style="font-size: 2rem;">屏蔽</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="553" style="font-size: 1.8rem;">电话</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="326" style="font-size: 1.8rem;">图纸</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="228" style="font-size: 1.6rem;">炸矿</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="1309" style="font-size: 2rem;">战力</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="448" style="font-size: 1.8rem;">合区</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="406" style="font-size: 1.8rem;">跨服</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="164" style="font-size: 1.4rem;">退游</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="102" style="font-size: 1.4rem;">超导</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="117" style="font-size: 1.4rem;">黑</span>
            </div>
            <div class="sentiment-legend">
                <span class="legend-item positive">正面词汇</span>
                <span class="legend-item neutral">中性词汇</span>
                <span class="legend-item negative">负面词汇</span>
            </div>
        </div>

        <!-- 重点关注玩家 -->
        <div class="key-players-card card">
            <h3><i class="fas fa-crown"></i> 重点关注玩家</h3>
            <div class="players-list">
                <div class="player-item">
    <div class="player-avatar">君</div>
    <div class="player-info">
        <div class="player-name">君怀</div>
        <div class="player-level">超R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">168条</span>
        <span class="sentiment-badge neutral">中性</span>
    </div>
    <div class="attention-reason">极度活跃的组织者和社交核心，频繁协调团队活动、讨论游戏改动，其言论和动向能反映团队的凝聚力和当前的主要关注点。</div>
</div>
<div class="player-item">
    <div class="player-avatar">世</div>
    <div class="player-info">
        <div class="player-name">世界第一帅</div>
        <div class="player-level">小R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">125条</span>
        <span class="sentiment-badge neutral">中性</span>
    </div>
    <div class="attention-reason">高频发言者，具有一定影响力，经常组织或评论游戏内冲突，言论能反映核心玩家对联盟对抗的态度。</div>
</div>
<div class="player-item">
    <div class="player-avatar">婳</div>
    <div class="player-info">
        <div class="player-name">婳皮</div>
        <div class="player-level">大R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">115条</span>
        <span class="sentiment-badge neutral">中性</span>
    </div>
    <div class="attention-reason">核心高战玩家，对游戏机制理解深入，讨论消费策略和游戏未来走向，言论具有影响力，是潜在的KOL。</div>
</div>
<div class="player-item">
    <div class="player-avatar">阖</div>
    <div class="player-info">
        <div class="player-name">阖家炸田鼠啊</div>
        <div class="player-level">超R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">115条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">高战力意见领袖，热衷于PvP，是服务器生态的核心驱动者。其言论极具煽动性，对游戏环境和玩家对立情绪影响大，其留存与否对服务器活跃度至关重要。</div>
</div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为词云添加点击效果
            const wordBubbles = document.querySelectorAll('.word-bubble');
            wordBubbles.forEach(bubble => {
                bubble.addEventListener('click', function() {
                    const sentiment = this.getAttribute('data-sentiment');
                    const frequency = this.getAttribute('data-frequency') || Math.floor(Math.random() * 100) + 1;
                    alert(`词汇: ${this.textContent}\n情感倾向: ${sentiment}\n出现频次: ${frequency}`);
                });
            });

            // 为健康度指示器添加悬停效果
            const healthIndicators = document.querySelectorAll('.health-indicator, .score-circle');
            healthIndicators.forEach(indicator => {
                indicator.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                });
                
                indicator.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 为卡片添加轻微的3D效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const rotateX = (y - centerY) / 20;
                    const rotateY = (centerX - x) / 20;
                    
                    this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-2px)`;
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
                });
            });
        });
    </script>
</body>
</html>