# -*- coding: utf-8 -*-
"""HTML转图片模块 - 使用Playwright将HTML报告转换为高质量图片"""

import os
import asyncio
from pathlib import Path

try:
    from playwright.async_api import async_playwright
except ImportError:
    print("❌ 错误: 未安装playwright。请运行: pip install playwright")
    print("然后运行: playwright install chromium")
    raise

class HTMLToImageConverter:
    """HTML转图片转换器"""
    
    async def html_to_image(self, html_file_path, output_image_path=None):
        """
        将HTML文件转换为高质量的PNG图片
        
        Args:
            html_file_path: HTML文件路径
            output_image_path: 输出图片路径（可选，默认自动生成）
            
        Returns:
            tuple: (success, image_path, file_size_mb)
        """
        if not os.path.exists(html_file_path):
            print(f"❌ 错误: HTML文件不存在 {html_file_path}")
            return False, None, 0
            
        # 自动生成输出文件名
        if not output_image_path:
            base_name = Path(html_file_path).stem
            output_image_path = f"{base_name}_screenshot.png" # 改为.png
            
        print(f"🖼️  正在将HTML转换为高质量PNG图片...")
        print(f"   📄 输入: {html_file_path}")
        print(f"   🖼️  输出: {output_image_path}")
        print(f"   📐 视口尺寸: 2400px宽度, 设备像素比: 3.0")
        
        try:
            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=True)
                
                # 创建上下文，并设置高清设备参数
                context = await browser.new_context(
                    viewport={'width': 2400, 'height': 3200},  # 增大视口尺寸，使得内容显示更大
                    device_scale_factor=3  # 提高像素比从2.0到3.0，获得更清晰的字体渲染
                )
                
                # 创建页面
                page = await context.new_page()
                
                # 转换为绝对路径
                html_file_abs_path = os.path.abspath(html_file_path)
                file_url = f"file://{html_file_abs_path}"
                
                print(f"🌐 加载页面: {file_url}")
                
                # 加载HTML文件
                await page.goto(file_url, wait_until="networkidle", timeout=60000)
                
                # 等待页面中的图片和字体加载完成
                await page.wait_for_load_state("networkidle")
                await page.evaluate("document.fonts.ready")
                await page.wait_for_timeout(1000) # 额外等待，确保渲染稳定
                
                # 截取整个页面为PNG
                screenshot_bytes = await page.screenshot(
                    path=None,  # 我们将手动保存
                    full_page=True,
                    type="png" # 使用无损PNG格式
                )
                
                await browser.close()
                
                # 保存文件
                with open(output_image_path, 'wb') as f:
                    f.write(screenshot_bytes)
                
                file_size_bytes = len(screenshot_bytes)
                file_size_mb = file_size_bytes / (1024 * 1024)
                
                print(f"✅ 图片生成成功!")
                print(f"   📊 文件大小: {file_size_mb:.2f} MB")
                print(f"   💾 保存位置: {output_image_path}")
                
                return True, output_image_path, file_size_mb
                
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False, None, 0

def convert_html_to_image(html_file_path, output_image_path=None):
    """
    同步接口：将HTML文件转换为图片
    
    Args:
        html_file_path: HTML文件路径
        output_image_path: 输出图片路径（可选）
        
    Returns:
        tuple: (success, image_path, file_size_mb)
    """
    converter = HTMLToImageConverter()
    return asyncio.run(converter.html_to_image(html_file_path, output_image_path))

# 测试函数
def test_conversion():
    """测试HTML转图片功能"""
    # 查找data目录下的最新HTML文件
    data_dir = 'data'
    if not os.path.exists(data_dir):
        print("❌ data目录不存在")
        return False
        
    html_files = [f for f in os.listdir(data_dir) if f.startswith('game_sentiment_report_') and f.endswith('.html')]
    
    if not html_files:
        print("❌ 未找到HTML报告文件")
        return False
    
    # 按文件名排序，取最新的
    html_files.sort(reverse=True)
    html_file = os.path.join(data_dir, html_files[0])
    
    print(f"🧪 测试HTML转图片功能")
    print(f"📄 测试文件: {html_file}")
    
    success, image_path, size_mb = convert_html_to_image(html_file)
    
    if success:
        print(f"✅ 测试成功! 图片已保存: {image_path} ({size_mb:.2f} MB)")
        return True
    else:
        print("❌ 测试失败!")
        return False

if __name__ == "__main__":
    test_conversion() 